{"name": "@vitejs/plugin-basic-ssl", "version": "1.2.0", "license": "MIT", "author": "Evan <PERSON> and Vite Contributors", "files": ["dist"], "main": "./dist/index.cjs", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "exports": {".": {"import": "./dist/index.mjs", "require": "./dist/index.cjs"}}, "engines": {"node": ">=14.21.3"}, "repository": {"type": "git", "url": "git+https://github.com/vitejs/vite-plugin-basic-ssl.git"}, "bugs": {"url": "https://github.com/vitejs/vite-plugin-basic-ssl/issues"}, "homepage": "https://github.com/vitejs/vite-plugin-basic-ssl/#readme", "peerDependencies": {"vite": "^3.0.0 || ^4.0.0 || ^5.0.0 || ^6.0.0"}, "devDependencies": {"@types/node": "^22.10.0", "conventional-changelog-cli": "^5.0.0", "enquirer": "^2.4.1", "execa": "^9.5.1", "minimist": "^1.2.8", "node-forge": "^1.3.1", "picocolors": "^1.1.1", "prettier": "^3.4.1", "semver": "^7.6.3", "tsx": "^4.19.2", "unbuild": "^2.0.0", "vite": "^6.0.1", "vitest": "^2.1.6"}, "scripts": {"dev": "unbuild --stub", "build": "unbuild && tsx scripts/patchCJS.ts", "test": "vitest run", "release": "tsx -C import scripts/release.ts", "changelog": "conventional-changelog -p angular -i CHANGELOG.md -s"}}