{"version": 3, "file": "legacy.js", "sourceRoot": "https://raw.githubusercontent.com/fb55/domutils/0ab8bcf1ecfc70dfc93291a4cb2496578ac25e9c/src/", "sources": ["legacy.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,KAAK,EAAE,MAAM,EAAoB,MAAM,YAAY,CAAC;AAE7D,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,eAAe,CAAC;AAqBhD;;GAEG;AACH,MAAM,MAAM,GAGR;IACA,QAAQ,CAAC,IAAI;QACT,IAAI,OAAO,IAAI,KAAK,UAAU,EAAE,CAAC;YAC7B,OAAO,CAAC,IAAa,EAAE,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC7D,CAAC;aAAM,IAAI,IAAI,KAAK,GAAG,EAAE,CAAC;YACtB,OAAO,KAAK,CAAC;QACjB,CAAC;QACD,OAAO,CAAC,IAAa,EAAE,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC;IAChE,CAAC;IACD,QAAQ,CAAC,IAAI;QACT,IAAI,OAAO,IAAI,KAAK,UAAU,EAAE,CAAC;YAC7B,OAAO,CAAC,IAAa,EAAE,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC9C,CAAC;QACD,OAAO,CAAC,IAAa,EAAE,EAAE,CAAC,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC;IACjD,CAAC;IACD,YAAY,CAAC,IAAI;QACb,IAAI,OAAO,IAAI,KAAK,UAAU,EAAE,CAAC;YAC7B,OAAO,CAAC,IAAa,EAAE,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC9D,CAAC;QACD,OAAO,CAAC,IAAa,EAAE,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC;IACjE,CAAC;CACJ,CAAC;AAEF;;;;;;;;GAQG;AACH,SAAS,cAAc,CACnB,MAAc,EACd,KAAwD;IAExD,IAAI,OAAO,KAAK,KAAK,UAAU,EAAE,CAAC;QAC9B,OAAO,CAAC,IAAa,EAAE,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC;IACzE,CAAC;IACD,OAAO,CAAC,IAAa,EAAE,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,KAAK,CAAC;AAC5E,CAAC;AAED;;;;;;;;GAQG;AACH,SAAS,YAAY,CAAC,CAAW,EAAE,CAAW;IAC1C,OAAO,CAAC,IAAa,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC;AACjD,CAAC;AAED;;;;;;;GAOG;AACH,SAAS,WAAW,CAAC,OAAwB;IACzC,MAAM,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,EAAE;QAC3C,MAAM,KAAK,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC;QAC3B,OAAO,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,CAAC;YACpD,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC;YACpB,CAAC,CAAC,cAAc,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;IACrC,CAAC,CAAC,CAAC;IAEH,OAAO,KAAK,CAAC,MAAM,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC;AAClE,CAAC;AAED;;;;;;;GAOG;AACH,MAAM,UAAU,WAAW,CAAC,OAAwB,EAAE,IAAa;IAC/D,MAAM,IAAI,GAAG,WAAW,CAAC,OAAO,CAAC,CAAC;IAClC,OAAO,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;AACpC,CAAC;AAED;;;;;;;;;GASG;AACH,MAAM,UAAU,WAAW,CACvB,OAAwB,EACxB,KAA0B,EAC1B,OAAgB,EAChB,QAAgB,QAAQ;IAExB,MAAM,IAAI,GAAG,WAAW,CAAC,OAAO,CAAC,CAAC;IAClC,OAAO,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;AAC3D,CAAC;AAED;;;;;;;;GAQG;AACH,MAAM,UAAU,cAAc,CAC1B,EAAsC,EACtC,KAA0B,EAC1B,OAAO,GAAG,IAAI;IAEd,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC;QAAE,KAAK,GAAG,CAAC,KAAK,CAAC,CAAC;IAC3C,OAAO,OAAO,CAAC,cAAc,CAAC,IAAI,EAAE,EAAE,CAAC,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;AAC7D,CAAC;AAED;;;;;;;;;GASG;AACH,MAAM,UAAU,oBAAoB,CAChC,OAA6C,EAC7C,KAA0B,EAC1B,OAAO,GAAG,IAAI,EACd,QAAgB,QAAQ;IAExB,OAAO,MAAM,CACT,MAAM,CAAC,UAAU,CAAC,CAAC,OAAO,CAAC,EAC3B,KAAK,EACL,OAAO,EACP,KAAK,CACK,CAAC;AACnB,CAAC;AAED;;;;;;;;;GASG;AACH,MAAM,UAAU,sBAAsB,CAClC,SAA+C,EAC/C,KAA0B,EAC1B,OAAO,GAAG,IAAI,EACd,QAAgB,QAAQ;IAExB,OAAO,MAAM,CACT,cAAc,CAAC,OAAO,EAAE,SAAS,CAAC,EAClC,KAAK,EACL,OAAO,EACP,KAAK,CACK,CAAC;AACnB,CAAC;AAED;;;;;;;;;GASG;AACH,MAAM,UAAU,oBAAoB,CAChC,IAAoD,EACpD,KAA0B,EAC1B,OAAO,GAAG,IAAI,EACd,QAAgB,QAAQ;IAExB,OAAO,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,IAAc,CAAC,EAAE,KAAK,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC;AAC7E,CAAC"}