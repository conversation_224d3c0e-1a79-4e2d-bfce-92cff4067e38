{"name": "custom-event", "version": "1.0.1", "description": "Cross-browser `CustomEvent` constructor", "main": "index.js", "scripts": {"test": "make test"}, "repository": {"type": "git", "url": "git://github.com/webmodules/custom-event.git"}, "keywords": ["dom", "browser", "event", "custom", "customevent", "constructor"], "author": "<PERSON> <<EMAIL>> (http://n8.io/)", "license": "MIT", "bugs": {"url": "https://github.com/webmodules/custom-event/issues"}, "homepage": "https://github.com/webmodules/custom-event", "devDependencies": {"zuul": "~1.16.3"}}