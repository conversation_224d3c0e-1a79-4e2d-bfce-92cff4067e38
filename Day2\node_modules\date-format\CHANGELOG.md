# date-format Changelog

## 4.0.14

- [chore(deps-dev): updated dependencies](https://github.com/nomiddlename/date-format/pull/86) - thanks [@lamweili](https://github.com/lamweili)
  - chore(deps-dev): bump eslint from 4.19.1 to 8.24.0
  - chore(deps-dev): bump eslint-plugin-mocha from 4.12.1 to 10.1.0
  - chore(deps-dev): bump mocha from 5.2.0 to 10.0.0
  - chore(deps-dev): bump nyc from 13.0.0 to 15.1.0
- [ci: manually downgrade dev dependencies for older versions](https://github.com/nomiddlename/date-format/pull/87) - thanks [@lamweili](https://github.com/lamweili)
- [ci: separated npm audit](https://github.com/nomiddlename/date-format/pull/85) - thanks [@lamweili](https://github.com/lamweili)
- [ci: updated codeql from v1 to v2](https://github.com/nomiddlename/date-format/pull/80) - thanks [@lamweili](https://github.com/lamweili)

## 4.0.13

- [ci: added tests for Node.js 4.x, 6.x, 8.x, 10.x, 18.x](https://github.com/nomiddlename/date-format/pull/75) - thanks [@lamweili](https://github.com/lamweili)
  - [chore(deps-dev): bump hosted-git-info from 2.6.0 to 2.8.9](https://github.com/nomiddlename/date-format/pull/76) - thanks [@Dependabot](https://github.com/dependabot)
  - [chore(deps-dev): bump handlebars from 4.0.11 to 4.7.7 ](https://github.com/nomiddlename/date-format/pull/77) - thanks [@Dependabot](https://github.com/dependabot)
  - [chore(deps-dev): bump y18n from 3.2.1 to 3.2.2](https://github.com/nomiddlename/date-format/pull/78) - thanks [@Dependabot](https://github.com/dependabot)

## 4.0.12

- [ci: added tests for Node.js 8.x, 10.x, 18.x](https://github.com/nomiddlename/date-format/pull/73) - thanks [@lamweili](https://github.com/lamweili)
- [docs: renamed peteriman to lamweili](https://github.com/nomiddlename/date-format/pull/72) - thanks [@lamweili](https://github.com/lamweili)

## 4.0.11

- [fix: date parsing errors (wrong month due to days overflow)](https://github.com/nomiddlename/date-format/pull/68) - thanks [@lamweili](https://github.com/lamweili)
  - [test: use new Date(0) instead of new Date() before setting every field]() - thanks [@lamweili](https://github.com/lamweili)
- [chore(deps-dev): updated dependencies](https://github.com/nomiddlename/date-format/pull/70) - thanks [@lamweili](https://github.com/lamweili)
  - chore(deps-dev): bump eslint from 8.15.0 to 8.16.0
  - chore(deps-dev): bump eslint-plugin-mocha from 10.0.4 to 10.0.5
  - chore(deps-dev): updated package-lock.json

## 4.0.10

- [chore(deps-dev): updated dependencies](https://github.com/nomiddlename/date-format/pull/66) - thanks [@lamweili](https://github.com/lamweili)
  - chore(deps-dev): bump eslint from 8.14.0 to 8.15.0
  - chore(deps-dev): bump mocha from 9.2.2 to 10.0.0
  - chore(deps-dev): updated package-lock.json

## 4.0.9

- build: is exactly the same as 4.0.8 and is a re-published 4.0.8 to npm

## 4.0.8

- [chore(deps-dev): updated dependencies](https://github.com/nomiddlename/date-format/pull/60) - thanks [@lamweili](https://github.com/lamweili)
  - chore(deps-dev): bump eslint from 8.13.0 to 8.14.0
  - chore(deps-dev): updated package-lock.json
- [chore(deps-dev): updated dependencies](https://github.com/nomiddlename/date-format/pull/59) - thanks [@lamweili](https://github.com/lamweili)
  - chore(deps-dev): updated package-lock.json

## 4.0.7

- [chore(deps-dev): updated dependencies](https://github.com/nomiddlename/date-format/pull/57) - thanks [@lamweili](https://github.com/lamweili)
  - chore(deps-dev): bump eslint-plugin-mocha from 10.0.3 to 10.0.4
  - chore(deps-dev): updated package-lock.json
- [chore(deps-dev): updated dependencies](https://github.com/nomiddlename/date-format/pull/54) - thanks [@lamweili](https://github.com/lamweili)
  - chore(deps-dev): bump eslint from 8.11.0 to 8.13.0
  - chore(deps-dev): updated package-lock.json

## 4.0.6

- [chore(deps-dev): updated dependencies](https://github.com/nomiddlename/date-format/pull/52) - thanks [@lamweili](https://github.com/lamweili)
  - chore(deps-dev): updated package-lock.json

## 4.0.5

- [test: better test coverage instead of ignoring](https://github.com/nomiddlename/date-format/pull/48) - thanks [@lamweili](https://github.com/lamweili)
- [docs: updated README.md with badges](https://github.com/nomiddlename/date-format/pull/50) thanks [@lamweili](https://github.com/lamweili)
- [chore(deps-dev): updated dependencies](https://github.com/nomiddlename/date-format/pull/49) - thanks [@lamweili](https://github.com/lamweili)
  - chore(deps-dev): bump eslint from 8.10.0 to 8.11.0
  - chore(deps-dev): bump mocha from 9.2.1 to 9.2.2
  - chore(deps-dev): updated package-lock.json

## 4.0.4

- chore(deps-dev): updated dependencies - thanks [@lamweili](https://github.com/lamweili)
  - [chore(deps-dev): bump eslint from 8.8.0 to 8.10.0 and mocha from 9.2.0 to 9.2.1](https://github.com/nomiddlename/date-format/pull/46) 
  - [chore(deps-dev): bump eslint from 8.7.0 to 8.8.0 and mocha from 9.1.4 to 9.2.0](https://github.com/nomiddlename/date-format/pull/45) 
  - [chore(deps-dev): updated package-lock.json](https://github.com/nomiddlename/date-format/pull/44) 

## 4.0.3

- [test: 100% test coverage](https://github.com/nomiddlename/date-format/pull/42) - thanks [@lamweili](https://github.com/lamweili)
- chore(deps-dev): updated dependencies - thanks [@lamweili](https://github.com/lamweili)
  - [chore(deps-dev): bump eslint from 8.6.0 to 8.7.0 and mocha from 9.1.3 to 9.1.4](https://github.com/nomiddlename/date-format/pull/41) 

## 4.0.2

- [build: not to publish misc files to NPM](https://github.com/nomiddlename/date-format/pull/39) - thanks [@lamweili](https://github.com/lamweili)
- docs: CHANGELOG.md
  - [docs: removed "log4js" from title of CHANGELOG.md](https://github.com/nomiddlename/date-format/pull/37) - thanks [@joshuabremerdexcom](https://github.com/joshuabremerdexcom)
  - [docs: added "date-format" to title of CHANGELOG.md](https://github.com/nomiddlename/date-format/commit/64a95d0386853692d7d65174f94a0751e775f7ce#diff-06572a96a58dc510037d5efa622f9bec8519bc1beab13c9f251e97e657a9d4ed) - thanks [@lamweili](https://github.com/lamweili)
- chore(deps-dev): updated dependencies - thanks [@lamweili](https://github.com/lamweili)
  - [chore(deps-dev): bump eslint-plugin-mocha from 5.3.0 to 10.0.3](https://github.com/nomiddlename/date-format/pull/38) 

## 4.0.1

- build: is exactly the same as 4.0.0 and is a re-published 4.0.0 to npm

## 4.0.0

- [fix: timezone format to include colon separator](https://github.com/nomiddlename/date-format/pull/27) - thanks [@lamweili](https://github.com/lamweili)
  - [test: have a test case for timezone with colon](https://github.com/nomiddlename/date-format/pull/32) - thanks [@lamweili](https://github.com/lamweili)
- [docs: updated README.md with more examples and expected output](https://github.com/nomiddlename/date-format/pull/33) - thanks [@lamweili](https://github.com/lamweili)
- chore(deps-dev): updated dependencies
  - [chore(deps-dev): bump should-util from 1.0.0 to 1.0.1](https://github.com/nomiddlename/date-format/pull/31) - thanks [@lamweili](https://github.com/lamweili)
  - [chore(deps-dev): bump eslint from 5.16.0 to 8.6.0 and mocha from 5.2.0 to 9.1.3](https://github.com/nomiddlename/date-format/pull/30) - thanks [@lamweili](https://github.com/lamweili)
  - [chore(deps-dev): bump acorn from 6.2.0 to 6.4.2](https://github.com/nomiddlename/date-format/pull/29) - thanks [@Dependabot](https://github.com/dependabot)
  - [chore(deps-dev): bump lodash from 4.17.14 to 4.17.21](https://github.com/nomiddlename/date-format/pull/26) - thanks [@Dependabot](https://github.com/dependabot)

## Previous versions

Change information for older versions can be found by looking at the milestones in github.
