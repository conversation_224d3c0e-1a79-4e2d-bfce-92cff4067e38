# Installation
> `npm install --save @types/jasmine`

# Summary
This package contains type definitions for jasmine (http://jasmine.github.io).

# Details
Files were exported from https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/jasmine.

### Additional Details
 * Last updated: Mon, 05 May 2025 21:02:40 GMT
 * Dependencies: none

# Credits
These definitions were written by [<PERSON>](https://github.com/b<PERSON><PERSON><PERSON>), [<PERSON>](https://github.com/theodorejb), [<PERSON>](https://github.com/david<PERSON>sson), [<PERSON><PERSON>](https://github.com/lukas-zech-software), [<PERSON>](https://github.com/Engineer2B), [<PERSON>](https://github.com/cyungmann), [<PERSON>](https://github.com/Roaders), [<PERSON><PERSON><PERSON>](https://github.com/devoto13), [<PERSON><PERSON>](https://github.com/fdim), [<PERSON><PERSON>](https://github.com/kolodny), [<PERSON>](https://github.com/step<PERSON><PERSON>), [<PERSON><PERSON><PERSON>berg](https://github.com/djungowski), [Chives](https://github.com/chivesrs), [kirjs](https://github.com/kirjs), and [Dmitry Semigradsky](https://github.com/<PERSON>gradsky).
